# 瀚高数据库集成需求文档

## 项目概述

### 基本信息
- **项目名称**: seatom-server
- **数据库类型**: 瀚高数据库 (HighgoDB)
- **数据库版本**: V4.5 安全版
- **兼容性**: 基于 PostgreSQL，但使用 SM3 国密算法

### 环境信息
- **Docker 容器**: `highgodb-dev`
- **镜像**: `xuxuclassmate/highgo:latest`
- **端口映射**: `0.0.0.0:5866->5866/tcp`
- **数据库实例**:
  - 开发库: `seatom_dev` (主机: localhost, 端口: 5866)

## 核心问题与解决方案

### 1. SM3 认证兼容性问题

#### 问题描述
- **错误信息**: "Unknown authentication message type 13"
- **根本原因**: 瀚高数据库安全版使用 SM3 国密算法，标准 PostgreSQL 客户端不支持
- **影响范围**: Node.js `pg` 客户端库无法连接

#### 当前数据库配置
```sql
-- 生产环境配置确认
password_encryption = sm3  -- 使用国密 SM3 算法
```

### 2. 解决方案对比

| 方案 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| 创建 MD5 用户 | 兼容性好，风险可控 | 安全性较低 | ⭐⭐⭐⭐ |
| 使用 ODBC 驱动 | 原生支持 SM3 | 配置复杂 | ⭐⭐⭐ |
| 全局改为 MD5 | 简单直接 | 安全风险高 | ⭐⭐ |
| 连接代理 | 透明转换 | 架构复杂 | ⭐⭐ |

### 3. 推荐实施方案

#### 方案A: 创建专用 MD5 用户（已实施）
```sql
-- 创建 MD5 加密用户
SET password_encryption TO 'md5';
CREATE USER highgo PASSWORD 'Highgo@123';
GRANT CONNECT ON DATABASE seatom_dev TO highgo;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO highgo;
```

#### 方案B: 创建 SM3 用户访问 seatom_dev（已实施）
```sql
-- 创建 SM3 加密用户
SET password_encryption TO 'sm3';
CREATE USER highdb_user PASSWORD 'Wufei123!';
GRANT CONNECT ON DATABASE seatom_dev TO highdb_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO highdb_user;
```

## 用户账号管理

### 已创建用户

#### 1. highgo (MD5)
- **用途**: 兼容标准 PostgreSQL 客户端的普通用户
- **加密方式**: MD5
- **连接信息**: localhost:5866
- **数据库**: seatom_dev
- **密码**: `Highgo@123`
- **权限**: 访问 seatom_dev 数据库

#### 2. highdb_user (SM3)
- **用途**: 访问 seatom_dev 开发环境的 SM3 用户
- **加密方式**: SM3 (国密)
- **连接信息**: localhost:5866
- **数据库**: seatom_dev
- **密码**: `Wufei123!`
- **权限**: 完整访问 seatom_dev 数据库所有表

### pg_hba.conf 配置

```bash
# MD5 用户配置
host    seatom_dev    highgo         localhost     md5

# SM3 用户配置
host    seatom_dev    highdb_user    localhost     sm3

# 默认配置
host    all           all            0.0.0.0/0     sm3
```

## 项目集成配置

### 应用配置文件位置
- **瀚高数据库驱动**: `app/helper/datasource/highgodb.js` ✅ **已升级为官方 highgodb 包**
- **数据源索引**: `app/helper/datasource/index.js`
- **常量定义**: `app/extend/constant.js`

### 驱动包升级详情
- **原驱动**: `pg` (PostgreSQL 标准驱动，不支持 SM3)
- **新驱动**: `highgodb` (瀚高官方驱动，完全支持 SM3 和 MD5)
- **升级优势**:
  - ✅ 原生支持 SM3 国密认证
  - ✅ 完全兼容 MD5 认证
  - ✅ 优化的连接池性能
  - ✅ 增强的错误处理
  - ✅ 更好的瀚高数据库特性支持

### 连接配置示例

#### 使用 MD5 用户连接
```javascript
const config = {
  host: 'localhost',
  port: 5866,
  user: 'highgo',
  password: 'Highgo@123',
  database: 'seatom_dev',
  ssl: false,
  connectionTimeoutMillis: 30000
}
```

#### 使用 SM3 用户连接开发环境
```javascript
const config = {
  host: 'localhost',  // Docker 容器
  port: 5866,
  user: 'highdb_user',
  password: 'Wufei123!',
  database: 'seatom_dev',
  ssl: false,
  connectionTimeoutMillis: 30000
}
```

## 测试脚本

### 可用测试脚本
1. **`scripts/test-highgodb-connection.js`** - 基础连接测试
2. **`scripts/test-highgodb-connection-enhanced.js`** - 增强版连接测试
3. **`scripts/test-md5-user-connection.js`** - MD5 用户连接测试
4. **`scripts/test-sm3-user-connection.js`** - SM3 用户连接测试

### 用户创建脚本
1. **`scripts/create-md5-user.sql`** - 创建 MD5 用户
2. **`scripts/create-sm3-user-for-seatom-dev.sql`** - 创建 SM3 用户
3. **`scripts/configure-highgo-docker-hba.sh`** - 配置 Docker 容器的 pg_hba.conf

## Docker 容器管理

### 容器信息
```bash
CONTAINER ID: c1354da498bf
IMAGE: xuxuclassmate/highgo:latest
PORTS: 0.0.0.0:5866->5866/tcp
NAME: highgodb-dev
```

### 常用操作
```bash
# 进入容器
docker exec -it highgodb-dev bash

# 查找配置文件
docker exec highgodb-dev find / -name "pg_hba.conf" 2>/dev/null

# 重新加载配置
docker exec highgodb-dev psql -U postgres -c "SELECT pg_reload_conf();"
```

## 安全考虑

### 密码策略
- **MD5 用户**: 使用强密码，定期轮换
- **SM3 用户**: 符合国密标准，安全性更高
- **网络限制**: 通过 pg_hba.conf 限制访问来源

### 权限控制
- **最小权限原则**: 只授予必要的数据库权限
- **用户隔离**: 不同环境使用不同用户
- **审计日志**: 启用连接和查询日志

## 故障排除

### 常见错误
1. **"Unknown authentication message type 13"**
   - 原因: SM3 认证不兼容
   - 解决: 使用 MD5 用户或 ODBC 驱动

2. **"authentication failed"**
   - 检查: 用户名、密码、pg_hba.conf 配置

3. **"database does not exist"**
   - 检查: 数据库名称、用户权限

### 调试步骤
1. 检查容器状态: `docker ps`
2. 查看日志: `docker logs highgodb-dev`
3. 测试连接: 运行相应的测试脚本
4. 检查配置: 查看 pg_hba.conf 和 postgresql.conf

## 更新记录

- **2025-01-31**: 初始文档创建
- **2025-01-31**: 添加 SM3 用户创建方案
- **2025-01-31**: 完善 Docker 容器管理说明
- **2025-01-31**: 更新用户信息 - MD5用户: highgo/Highgo@123, SM3用户: highdb_user/Wufei123!, 数据库: seatom_dev
- **2025-01-31**: 🎉 **重大升级** - 将 `app/helper/datasource/highgodb.js` 从 `pg` 包升级为瀚高官方 `highgodb` 包，完全支持 SM3 和 MD5 认证
