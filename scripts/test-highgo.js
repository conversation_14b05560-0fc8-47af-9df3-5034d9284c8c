const { Pool, Client } = require('highgodb')

console.log('🔍 测试瀚高数据库连接 (使用官方 highgodb 包)...')
console.log('🎯 重点测试: SM3 用户使用连接池连接')
console.log('=' * 60)

// 首先测试 SM3 用户使用连接池
console.log('🔐 测试 SM3 用户连接池方式...')

const sm3Pool = new Pool({
    user: 'highdb_user',
    host: 'localhost',
    database: 'seatom_dev',
    password: 'Wufei123!',
    port: 5866,
    // 连接池配置
    max: 5,                    // 最大连接数
    idleTimeoutMillis: 30000,  // 空闲超时
    connectionTimeoutMillis: 15000, // 连接超时
})

console.log('📋 SM3 连接池参数:')
console.log('   主机: localhost')
console.log('   端口: 5866')
console.log('   用户: highdb_user (SM3)')
console.log('   数据库: seatom_dev')
console.log('   最大连接数: 5')

// 测试连接池事件监听
sm3Pool.on('connect', () => {
    console.log('🔗 连接池建立新连接')
})

sm3Pool.on('error', (err) => {
    console.log('❌ 连接池错误:', err.message)
})

// 执行查询测试
sm3Pool.query('SELECT version(), current_user, current_database(), now() as connect_time;', (err, res) => {
    if (err) {
        console.log('❌ SM3 连接池查询失败:', err.message)
        console.log('错误详情:', err)
    } else {
        console.log('✅ SM3 连接池查询成功!')
        console.log('📊 查询结果:')
        console.log('   版本:', res.rows[0].version.substring(0, 50) + '...')
        console.log('   当前用户:', res.rows[0].current_user)
        console.log('   当前数据库:', res.rows[0].current_database)
        console.log('   连接时间:', res.rows[0].connect_time)
    }

    // 测试多次查询（验证连接复用）
    testMultipleQueries()
})

// 测试多次查询以验证连接池的连接复用
function testMultipleQueries() {
    console.log('\n🔄 测试连接池多次查询（验证连接复用）...')

    const queries = [
        'SELECT current_user as user_name;',
        'SELECT current_database() as db_name;',
        'SELECT count(*) as table_count FROM information_schema.tables WHERE table_schema = \'public\';'
    ]

    let completedQueries = 0
    const startTime = Date.now()

    queries.forEach((sql, index) => {
        sm3Pool.query(sql, (err, res) => {
            completedQueries++

            if (err) {
                console.log(`❌ 查询 ${index + 1} 失败:`, err.message)
            } else {
                console.log(`✅ 查询 ${index + 1} 成功:`, res.rows[0])
            }

            // 所有查询完成后，测试 Client 方式作为对比
            if (completedQueries === queries.length) {
                const duration = Date.now() - startTime
                console.log(`⏱️  连接池多查询总耗时: ${duration}ms`)

                // 关闭连接池并测试 Client 方式
                sm3Pool.end(() => {
                    console.log('🔒 SM3 连接池已关闭')
                    testSM3Client()
                })
            }
        })
    })
}

// 使用 client 方式 (SM3 用户) 作为对比
function testSM3Client() {
    console.log('\n🔄 测试 SM3 Client 方式连接（对比测试）...')

    const client = new Client({
        host: 'localhost',
        user: "highdb_user",
        password: "Wufei123!",
        database: "seatom_dev",
        port: 5866,
    })

    console.log('📋 SM3 Client 参数:')
    console.log('   主机: localhost')
    console.log('   端口: 5866')
    console.log('   用户: highdb_user (SM3)')
    console.log('   数据库: seatom_dev')

    const startTime = Date.now()

    client.connect((err) => {
        if (err) {
            console.log('❌ SM3 Client 连接失败:', err.message)
            return
        }

        client.query('SELECT version(), current_user, current_database();', (err, res) => {
            const duration = Date.now() - startTime

            if (err) {
                console.log('❌ SM3 Client 查询失败:', err.message)
            } else {
                console.log('✅ SM3 Client 查询成功!')
                console.log('📊 查询结果:', res.rows[0])
                console.log(`⏱️  Client 连接+查询总耗时: ${duration}ms`)
            }

            client.end()

            // 输出测试总结
            console.log('\n📝 测试总结:')
            console.log('   ✅ SM3 用户支持连接池方式')
            console.log('   ✅ SM3 用户支持 Client 方式')
            console.log('   🎯 建议在生产环境中使用连接池方式')
        })
    })
}

console.log('Press any key to exit');

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.on('data', process.exit.bind(process, 0));