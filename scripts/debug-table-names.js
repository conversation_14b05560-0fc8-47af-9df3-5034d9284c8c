const { Pool } = require('highgodb')

async function debugTableNames() {
  const config = {
    host: 'localhost',
    port: 5866,
    user: 'highgo',
    password: 'Highgo@123',
    database: 'seatom_dev',
    ssl: false,
    connectionTimeoutMillis: 30000
  }

  const pool = new Pool(config)
  
  try {
    console.log('=== 调试瀚高数据库表名问题 ===')
    console.log('连接配置:', config)
    
    // 1. 测试基本连接
    const client = await pool.connect()
    console.log('✅ 数据库连接成功')
    
    // 2. 查看所有 schema
    console.log('\n--- 查看所有 schema ---')
    const schemaResult = await client.query(`
      SELECT schema_name 
      FROM information_schema.schemata 
      ORDER BY schema_name
    `)
    console.log('所有 schema:', schemaResult.rows)
    
    // 3. 查看 public schema 中的表
    console.log('\n--- 查看 public schema 中的表 ---')
    const publicTablesResult = await client.query(`
      SELECT table_name, table_schema, table_type
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `)
    console.log('public schema 表数量:', publicTablesResult.rows.length)
    console.log('public schema 表列表:')
    publicTablesResult.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. ${JSON.stringify(row)}`)
    })
    
    // 4. 查看所有表（不限制 schema）
    console.log('\n--- 查看所有表（不限制 schema）---')
    const allTablesResult = await client.query(`
      SELECT table_name, table_schema, table_type
      FROM information_schema.tables
      WHERE table_type = 'BASE TABLE'
      ORDER BY table_schema, table_name
    `)
    console.log('所有表数量:', allTablesResult.rows.length)
    console.log('所有表列表:')
    allTablesResult.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. ${JSON.stringify(row)}`)
    })
    
    // 5. 检查是否有 base_dept 表
    console.log('\n--- 检查 base_dept 表 ---')
    const baseDeptResult = await client.query(`
      SELECT table_name, table_schema, table_type
      FROM information_schema.tables
      WHERE table_name = 'base_dept'
    `)
    console.log('base_dept 表信息:', baseDeptResult.rows)
    
    // 6. 测试原始 SQL（和代码中一样的）
    console.log('\n--- 测试原始 SQL ---')
    const originalSql = `
      SELECT table_name as TABLE_NAME
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `
    console.log('执行 SQL:', originalSql)
    const originalResult = await client.query(originalSql)
    console.log('原始 SQL 结果:')
    console.log('- 行数:', originalResult.rows.length)
    console.log('- 字段名:', originalResult.fields ? originalResult.fields.map(f => f.name) : 'N/A')
    originalResult.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. 原始数据:`, JSON.stringify(row))
      console.log(`     - row.table_name:`, row.table_name)
      console.log(`     - row.TABLE_NAME:`, row.TABLE_NAME)
      console.log(`     - row["table_name"]:`, row["table_name"])
      console.log(`     - row["TABLE_NAME"]:`, row["TABLE_NAME"])
    })
    
    client.release()
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
  } finally {
    await pool.end()
  }
}

debugTableNames().catch(console.error)
